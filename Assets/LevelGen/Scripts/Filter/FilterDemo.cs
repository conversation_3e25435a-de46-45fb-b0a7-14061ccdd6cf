using LevelGen.Scripts.Erosion;
using LevelGen.Scripts.Filter.Common;
using LevelGen.Scripts.Filter.Configs;
using UnityEngine;
using Sirenix.OdinInspector;

namespace LevelGen.Scripts.Filter
{
    /// <summary>
    /// Demo script để test các filter
    /// </summary>
    public class FilterDemo : MonoBehaviour
    {
        [Header("Test Settings")]
        [SerializeField] private int matrixWidth = 20;
        [SerializeField] private int matrixHeight = 20;
        [SerializeField] private FilterConfigGroup filterConfigGroup;

        [Header("Current Filter")]
        [SerializeField] private string currentFilterName = "Circle";
        
        [Header("Test Matrix")]
        [SerializeField, ReadOnly] private int[,] originalMatrix;
        [SerializeField, ReadOnly] private int[,] filteredMatrix;
        [SerializeField, ReadOnly] private int[,] layerMatrix;

        [Header("Visualization")]
        [SerializeField] private bool showInConsole = true;
        [SerializeField] private bool showAsGizmos = false;
        [SerializeField] private Color originalColor = Color.blue;
        [SerializeField] private Color filteredColor = Color.red;
        [SerializeField] private float gizmoSize = 0.5f;
        
        [Space]
        [SerializeField]
        private Color layerColor = Color.green;

        private void Start()
        {
            if (filterConfigGroup == null)
            {
                Debug.LogWarning("FilterManager not assigned! Please create FilterManagerNew asset and assign filter configs.");
                filterConfigGroup = ScriptableObject.CreateInstance<FilterConfigGroup>();
            }

            GenerateTestMatrix();
        }

        [Button("Generate Test Matrix")]
        public void GenerateTestMatrix()
        {
            originalMatrix = new int[matrixHeight, matrixWidth];
            
            // Tạo matrix test với pattern ngẫu nhiên
            for (int i = 0; i < matrixHeight; i++)
            {
                for (int j = 0; j < matrixWidth; j++)
                {
                    /*// Tạo pattern checkerboard với một số noise
                    bool isCheckerSquare = ((i / 2) + (j / 2)) % 2 == 0;
                    bool hasNoise = Random.value > -1f;
                    
                    originalMatrix[i, j] = (isCheckerSquare || hasNoise) ? 1 : 0;*/
                    originalMatrix[i, j] = 1;
                }
            }

            if (showInConsole)
            {
                Debug.Log("Original Matrix:");
                PrintMatrix(originalMatrix);
            }
        }

        [Button("Apply Current Filter")]
        public void ApplyCurrentFilter()
        {
            if (originalMatrix == null)
            {
                GenerateTestMatrix();
            }

            var filter = filterConfigGroup.GetFilterConfig(currentFilterName).CreateFilter();
            if (filter != null)
            {
                filteredMatrix = filter.Filter(originalMatrix);
                
                if (showInConsole)
                {
                    Debug.Log($"Filtered Matrix ({currentFilterName}):");
                    PrintMatrix(filteredMatrix);
                }
            }
            else
            {
                Debug.LogError($"Could not create filter: {currentFilterName}");
            }
        }       
        
        [Button("Apply Current Filter With Random")]
        public void ApplyCurrentFilterRandom()
        {
            if (originalMatrix == null)
            {
                GenerateTestMatrix();
            }
            
            var filter = filterConfigGroup.GetFilterConfig(currentFilterName).CreateRandomFilter();
            if (filter != null)
            {
                filteredMatrix = filter.Filter(originalMatrix);
                
                if (showInConsole)
                {
                    Debug.Log($"Filtered Matrix ({currentFilterName}):");
                    PrintMatrix(filteredMatrix);
                }
            }
            else
            {
                Debug.LogError($"Could not create filter: {currentFilterName}");
            }
        }

        [Button("Apply Random Filter")]
        public void ApplyRandomFilter()
        {
            if (originalMatrix == null)
            {
                GenerateTestMatrix();
            }
            
            var config = filterConfigGroup.GetRandomFilterConfig();
            Debug.Log("Random Filter: " + config.GetFilterName());

            var filter = config.CreateFilter();
            if (filter != null)
            {
                filteredMatrix = filter.Filter(originalMatrix);
                
                if (showInConsole)
                {
                    Debug.Log("Filtered Matrix (Random):");
                    PrintMatrix(filteredMatrix);
                }
            }
        }
        
        
        
        
        
        [Button("Apply Random Filter With Random")]
        public void ApplyRandomFilterRandom()
        {
            if (originalMatrix == null)
            {
                GenerateTestMatrix();
            }
            
            var config = filterConfigGroup.GetRandomFilterConfig();
            Debug.Log("Random Filter: " + config.GetFilterName());

            var filter = config.CreateRandomFilter();
            if (filter != null)
            {
                filteredMatrix = filter.Filter(originalMatrix);
                
                if (showInConsole)
                {
                    Debug.Log("Filtered Matrix (Random):");
                    PrintMatrix(filteredMatrix);
                }
            }
        }



        private int _index;
        
        [Button("Apply Filter Sequence")]
        public void ApplyFilterSequence()
        {
            if (originalMatrix == null)
            {
                GenerateTestMatrix();
            }

            _index++;
            
            var config = filterConfigGroup.GetFilterConfig(_index);
            Debug.Log("Random Filter: " + config.GetFilterName());

            var filter = config.CreateFilter();
            if (filter != null)
            {
                filteredMatrix = filter.Filter(originalMatrix);
                
                if (showInConsole)
                {
                    Debug.Log("Filtered Matrix (Random):");
                    PrintMatrix(filteredMatrix);
                }
            }
        }             
        
        
        [Button("Apply Filter Sequence Random")]
        public void ApplyFilterSequenceRandom()
        {
            if (originalMatrix == null)
            {
                GenerateTestMatrix();
            }

            _index++;
            
            var config = filterConfigGroup.GetFilterConfig(_index);
            Debug.Log("Random Filter: " + config.GetFilterName());

            var filter = config.CreateRandomFilter();
            if (filter != null)
            {
                filteredMatrix = filter.Filter(originalMatrix);
                
                if (showInConsole)
                {
                    Debug.Log("Filtered Matrix (Random):");
                    PrintMatrix(filteredMatrix);
                }
            }
        }            
        
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void Erode()
        {
            ApplyFilterSequenceRandom();

            layerMatrix = LayoutAlgorithms.Erode(filteredMatrix, 1, LayoutAlgorithms.Neighborhood.FourWay);
                
            if (showInConsole)
            {
                Debug.Log("Filtered Matrix (Random):");
                PrintMatrix(layerMatrix);
            }
        }      
        
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void ProbabilisticErode()
        {
            ApplyFilterSequenceRandom();

            layerMatrix = LayoutAlgorithms.ProbabilisticErode(filteredMatrix, 1);
                
            if (showInConsole)
            {
                Debug.Log("Filtered Matrix (Random):");
                PrintMatrix(layerMatrix);
            }
        }      
        
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void DirectionalErode()
        {
            ApplyFilterSequenceRandom();

            layerMatrix = LayoutAlgorithms.DirectionalErode(filteredMatrix, new Vector2Int[] { new Vector2Int(0, 1) }, 1);
                
            if (showInConsole)
            {
                Debug.Log("Filtered Matrix (Random):");
                PrintMatrix(layerMatrix);
            }
        }    
        
        [Button(ButtonSizes.Large, ButtonStyle.Box)]
        void Skeletonize()
        {
            ApplyFilterSequenceRandom();

            layerMatrix = LayoutAlgorithms.Skeletonize(filteredMatrix, 2);
                
            if (showInConsole)
            {
                Debug.Log("Filtered Matrix (Random):");
                PrintMatrix(layerMatrix);
            }
        }
        
        
        private void PrintMatrix(int[,] matrix)
        {
            if (matrix == null) return;

            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            string output = "";

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    output += matrix[i, j] == 0 ? "." : "█";
                }
                output += "\n";
            }

            Debug.Log(output);
        }

        private int CountNonZero(int[,] matrix)
        {
            if (matrix == null) return 0;

            int count = 0;
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    if (matrix[i, j] != 0)
                        count++;
                }
            }

            return count;
        }

        private void OnDrawGizmos()
        {
            if (!showAsGizmos) return;

            Vector3 startPos = transform.position;

            // Draw original matrix
            if (originalMatrix != null)
            {
                Gizmos.color = originalColor;
                DrawMatrixGizmos(originalMatrix, startPos);
            }

            // Draw filtered matrix
            if (filteredMatrix != null)
            {
                Gizmos.color = filteredColor;
                Vector3 offset = new Vector3(matrixWidth + 2, 0, 0);
                DrawMatrixGizmos(filteredMatrix, startPos + offset);
            }
            
            // Draw layer matrix
            if (layerMatrix != null)
            {
                Gizmos.color = layerColor;
                Vector3 offset = new Vector3(matrixWidth * 2 + 4, 0, 0);
                DrawMatrixGizmos(layerMatrix, startPos + offset);
            }
        }

        private void DrawMatrixGizmos(int[,] matrix, Vector3 startPos)
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    if (matrix[i, j] != 0)
                    {
                        Vector3 pos = startPos + new Vector3(j, 0, -i);
                        Gizmos.DrawCube(pos, Vector3.one * gizmoSize);
                    }
                }
            }
        }

        [Button("Create Filter Manager Asset")]
        public void CreateFilterManagerAsset()
        {
#if UNITY_EDITOR
            var asset = ScriptableObject.CreateInstance<FilterConfigGroup>();

            UnityEditor.AssetDatabase.CreateAsset(asset, "Assets/LevelGen/Scripts/Filter/DefaultFilterManager.asset");
            UnityEditor.AssetDatabase.SaveAssets();

            filterConfigGroup = asset;
            Debug.Log("FilterManager asset created at Assets/LevelGen/Scripts/Filter/DefaultFilterManager.asset");
            Debug.Log("Now create individual filter configs using Create > LevelGen > Filters menu and add them to the manager.");
#endif
        }
    }
}
